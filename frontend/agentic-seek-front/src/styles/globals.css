:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(222.2 47.4% 11.2%);
  --muted: hsl(210 40% 96.1%);
  --muted-foreground: hsl(215.4 16.3% 46.9%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(222.2 47.4% 11.2%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(222.2 47.4% 11.2%);
  --border: hsl(214.3 31.8% 91.4%);
  --input: hsl(214.3 31.8% 91.4%);
  --primary: hsl(222.2 47.4% 11.2%);
  --primary-foreground: hsl(210 40% 98%);
  --secondary: hsl(210 40% 96.1%);
  --secondary-foreground: hsl(222.2 47.4% 11.2%);
  --accent: hsl(210 40% 96.1%);
  --accent-foreground: hsl(222.2 47.4% 11.2%);
  --destructive: hsl(0 100% 50%);
  --destructive-foreground: hsl(210 40% 98%);
  --ring: hsl(215 20.2% 65.1%);
  --radius: 0.5rem;
}

.dark {
  --background: hsl(224 71% 4%);
  --foreground: hsl(213 31% 91%);
  --muted: hsl(223 47% 11%);
  --muted-foreground: hsl(215.4 16.3% 56.9%);
  --popover: hsl(224 71% 4%);
  --popover-foreground: hsl(215 20.2% 65.1%);
  --card: hsl(224 71% 4%);
  --card-foreground: hsl(213 31% 91%);
  --border: hsl(216 34% 17%);
  --input: hsl(216 34% 17%);
  --primary: hsl(210 40% 98%);
  --primary-foreground: hsl(222.2 47.4% 1.2%);
  --secondary: hsl(222.2 47.4% 11.2%);
  --secondary-foreground: hsl(210 40% 98%);
  --accent: hsl(216 34% 17%);
  --accent-foreground: hsl(210 40% 98%);
  --destructive: hsl(0 63% 31%);
  --destructive-foreground: hsl(210 40% 98%);
  --ring: hsl(216 34% 17%);
  --radius: 0.5rem;
}

[data-theme="dark"] {
  --background: #0a0a0a;
  --foreground: #fafafa;
  --card: #1a1a1a;
  --card-foreground: #fafafa;
  --popover: #1a1a1a;
  --popover-foreground: #fafafa;
  --primary: #fafafa;
  --primary-foreground: #0a0a0a;
  --secondary: #2a2a2a;
  --secondary-foreground: #fafafa;
  --muted: #1e1e1e;
  --muted-foreground: #a1a1aa;
  --accent: #6b7280;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #333333;
  --input: #333333;
  --ring: #6b7280;
}

[data-theme="light"] {
  --background: #ffffff;
  --foreground: #0a0a0a;
  --card: #ffffff;
  --card-foreground: #0a0a0a;
  --popover: #ffffff;
  --popover-foreground: #0a0a0a;
  --primary: #0a0a0a;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5;
  --secondary-foreground: #0a0a0a;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #6b7280;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #6b7280;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}

html,
body,
#root {
  height: 100%;
  overflow: hidden;
}

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 44px;
  height: 44px;
  padding: 0 12px;
  border-radius: 12px;
  border: 1px solid var(--border);
  background: var(--card);
  color: var(--foreground);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-toggle::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.theme-toggle:hover::before {
  left: 100%;
}

.theme-toggle:hover {
  background: #24292e;
  border-color: #24292e;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(36, 41, 46, 0.3);
}

.github-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 44px;
  height: 44px;
  padding: 0 12px;
  border-radius: 12px;
  border: 1px solid var(--border);
  background: var(--card);
  color: var(--foreground);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.github-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.github-link:hover::before {
  left: 100%;
}

.github-link:hover {
  background: #24292e;
  border-color: #24292e;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(36, 41, 46, 0.3);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}
